import { ref, reactive, watch } from 'vue';
import _ from 'lodash';
import apiClient from '@spa/services/api.client.js';
import useConfirm from '@spa/services/useConfirm';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper';

const api = apiClient;

export default function useCommonStore(apiUrl) {
    const confirm = useConfirm();
    const loaderStore = useLoaderStore();

    const serverPagination = ref({
        page: 1,
        rowsNumber: 0,
        sortBy: 'id',
        descending: true,
        rowsPerPage: 25,
    });
    const filters = ref({});
    const loading = ref(false);
    const ctxLoading = ref({});
    const enableLoader = ref(true);
    const all = ref([]);
    const form_rows = ref([]);
    const selected = ref([]);
    const formRef = ref(null);
    const formDialog = ref(false);
    const formData = ref({});
    const errors = ref({});
    const isFormValid = ref(false);
    const statusOptions = [
        { label: 'Active', value: 1 },
        { label: 'Inactive', value: 0 },
    ];
    const progresses = ref({});
    const notifySuccess = (message) => {
        window['Fire'].emit('axiosResponseSuccess', {
            message: message,
            time: 1000,
        });
    };
    const notifyError = (message) => {
        window['Fire'].emit('axiosResponseError', {
            message: message,
            time: 1000,
        });
    };

    const buildFormData = (formData, data, parentKey) => {
        if (
            data &&
            typeof data === 'object' &&
            !(data instanceof Date) &&
            !(data instanceof File)
        ) {
            Object.keys(data).forEach((key) => {
                buildFormData(formData, data[key], parentKey ? `${parentKey}[${key}]` : key);
            });
        } else {
            const value = data == null ? '' : data;

            formData.append(parentKey, value);
        }
    };
    const jsonToFormData = (data) => {
        const formData = new FormData();
        buildFormData(formData, data);
        return formData;
    };
    const store = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        return new Promise((resolve, reject) => {
            api.post(`/api/` + apiUrl, jsonToFormData(formData.value))
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                    const storeUrl = ref('medias');
                    const image = ref('');
                    const saveFile = function (file) {
                        image.value = file[0];
                        const modelData = new FormData();
                        modelData.append('uploaded-file', image.value);
                        modelData.append('upload-for', 'media');
                        api.post('api/app/storage/upload', modelData, {
                            headers: {
                                'Content-Type': 'multipart/form-data',
                            },
                        })
                            .then((response) => {
                                all.value = response.data;
                                form_rows.value = _.cloneDeep(response.data);
                                globalHelper.methods.showPopupSuccess(
                                    'Media Uploaded Successfully',
                                    'Success'
                                );
                                store.fetchPaged();
                            })
                            .catch((err) => {
                                errors.value = err.response.data.errors;
                            });
                    };

                    const {
                        serverPagination,
                        filters,
                        loading,
                        all,
                        t,
                        q,
                        formRef,
                        formDialog,
                        formData,
                        errors,
                        getAll,
                        onRequest,
                        store,
                        update,
                        remove,
                        createFunction,
                        closeFunction,
                        edit,
                        submitFormData,
                        confirmDelete,
                        fetchPaged,
                        clearFunction,
                    } = useCommonStore(storeUrl.value, 'app');

                    return {
                        saveFile,
                        serverPagination,
                        filters,
                        loading,
                        all,
                        t,
                        q,
                        formRef,
                        formDialog,
                        formData,
                        errors,
                        getAll,
                        onRequest,
                        store,
                        update,
                        remove,
                        createFunction,
                        closeFunction,
                        edit,
                        submitFormData,
                        confirmDelete,
                        fetchPaged,
                        clearFunction,
                    };
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                });
        });
    };

    const update = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('update', true);
        return new Promise((resolve, reject) => {
            const form = jsonToFormData(formData.value);
            // form.append('_method','PUT')
            api.post(`/api/${apiUrl}/${formData.value.id}`, form)
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    contextLoading('update', false);
                });
        });
    };

    const changeStatusOtherColumn = (column = 'status') => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('change-status', true);
        return new Promise((resolve, reject) => {
            const form = jsonToFormData(formData.value);
            form.append('_method', 'PUT');
            api.post(`/api/${apiUrl}/${formData.value.id}/status-change/${column}`, form)
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                })
                .finally(() => {
                    contextLoading('change-status', false);
                });
        });
    };
    const updateDropDown = (id, payload) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('update-dropdown', true);
        return new Promise((resolve, reject) => {
            const form = jsonToFormData(formData.value);
            // form.append('_method','PUT')
            api.post(`/api/${apiUrl}/${id}/update-dropdown`, payload)
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                })
                .finally(() => {
                    contextLoading('update-dropdown', false);
                });
        });
    };
    const remove = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('remove', true);
        loaderStore.startContextLoading('remove');
        return new Promise((resolve, reject) => {
            const items = _.map(selected.value, (item) => {
                return item?.id;
            });
            api.post(`/api/${apiUrl}/delete`, {
                delete_rows: items,
            })
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    contextLoading('remove', false);
                    loaderStore.stopContextLoading('remove');
                });
        });
    };
    const fetchPaged = (callback = () => {}) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('fetch-paged', true);
        api.get(`/api/` + apiUrl, {
            params: {
                ...serverPagination.value,
                filters: JSON.stringify(filters.value),
            },
        })
            .then((response) => {
                if (enableLoader.value) {
                    loading.value = false;
                }
                all.value = response.data;
                form_rows.value = _.cloneDeep(response.data);
                if (response.data.meta && response.data.meta.total) {
                    serverPagination.value.rowsNumber = response.data.meta.total;
                }
                callback();
            })
            .catch((error) => {
                all.value = [];
                form_rows.value = [];
                serverPagination.value.rowsNumber = 0;
            })
            .finally(() => {
                if (enableLoader.value) {
                    loading.value = false;
                }
                contextLoading('fetch-paged', false);
            });
    };
    const toggleStatus = async (id) => {
        const res = await api.put(`/api/${apiUrl}/${id}/change-status`);
        if (res.status === 200) {
            globalHelper.methods.showPopupSuccess('Status changed successfully.', 'Success');
            fetchPaged();
            return true;
        } else {
            globalHelper.methods.showPopupError('Oops! Something went wrong.', 'Error');
            return false;
        }
    };
    const onRequest = ({ pagination }) => {
        serverPagination.value = pagination;
        fetchPaged();
    };
    const getAll = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('get-all', true);
        api.get(`/api/${apiUrl}/all`, {
            params: {
                ...filters.value,
            },
        })
            .then((response) => {
                let res = response.data;
                all.value = res.data;
                form_rows.value = _.cloneDeep(res.data);
            })
            .finally(() => {
                if (enableLoader.value) {
                    loading.value = false;
                }
                contextLoading('get-all', false);
            });
    };
    const fetchDataById = async (id) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('fetch-by-id', true);
        const res = await api.get(`/api/${apiUrl}/${id}`);
        formData.value = Object.assign({}, res.data);
        if (enableLoader.value) {
            loading.value = false;
        }
        return res;
        // return  api.get(`/api/${apiUrl}/${id}`)
        //   .then(response => {
        //     let res = response.data;
        //     formData.value = Object.assign({}, res.data);
        //   })
        //   .finally(() => {
        //     loading.value = false;
        //   });
    };
    const fetchDataBySlug = async (slug) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        const res = await api.get(`/api/${apiUrl}/with-slug/${slug}`);
        formData.value = Object.assign({}, res.data);
        if (enableLoader.value) {
            loading.value = false;
        }
        contextLoading('fetch-by-id', false);
        return res;
    };

    function createFunction() {
        formDialog.value = true;
        formData.value = {};
        errors.value = {};
    }

    function clearFunction() {
        filters.value = {};
        fetchPaged();
    }

    function closeFunction() {
        const redirect_page = formData.value?.redirect_page;
        const redirect = formData.value?.redirect;
        formData.value = {};
        errors.value = {};
        formDialog.value = false;
        if (redirect) {
            // router.push({
            //   name: redirect_page
            // })
        }
    }

    const edit = (item) => {
        formData.value = Object.assign({}, item);
        formDialog.value = true;
    };
    const submitFormData = () => {
        console.log('validation: ', formRef.value);
        formRef.value.validate().then((success) => {
            if (success) {
                console.log('success: ', success);
                if (formData.value.id) {
                    update()
                        .then((res) => {
                            closeFunction();
                            globalHelper.methods.showPopupSuccess(
                                'Updated successfully',
                                'Success'
                            );
                            fetchPaged();
                        })
                        .catch((err) => {
                            errors.value = err.response.data.errors;
                        });
                } else {
                    store()
                        .then((res) => {
                            closeFunction();
                            globalHelper.methods.showPopupSuccess(
                                'Created successfully',
                                'Success'
                            );
                            fetchPaged();
                        })
                        .catch((err) => {
                            errors.value = err.response.data.errors;
                        });
                }
            }
        });
    };
    const submitKendoForm = () => {
        console.log('isValid', isFormValid);
        if (isFormValid) {
            if (formData.value.id) {
                update()
                    .then((res) => {
                        closeFunction();
                        globalHelper.methods.showPopupSuccess('Updated successfully', 'Success');
                        fetchPaged();
                    })
                    .catch((err) => {
                        errors.value = err.response.data.errors;
                    });
            } else {
                store()
                    .then((res) => {
                        closeFunction();
                        globalHelper.methods.showPopupSuccess('Created successfully', 'Success');
                        fetchPaged();
                    })
                    .catch((err) => {
                        errors.value = err.response.data.errors;
                    });
            }
        }
    };
    const objToFormData = (obj) => {
        const newFormData = new FormData();
        Object.keys(obj).forEach((key) => {
            newFormData.append(key, obj[key]);
        });
    };
    const confirmDelete = (item, options = {}) => {
        const {
            message = 'Are you sure you want to delete this item? This action cannot be undone.',
            header = 'Delete Item?',
            icon = 'pi pi-exclamation-triangle',
            variant = 'danger',
            acceptLabel = 'Delete',
            rejectLabel = 'Cancel',
            width = 500,
            onAccept = null,
            onReject = null,
            onHide = null,
        } = options;
        confirm.require({
            message,
            header,
            icon,
            variant,
            acceptLabel,
            rejectLabel,
            width,
            accept: async () => {
                selected.value.push(item);

                try {
                    await remove();

                    selected.value = [];

                    if (typeof onAccept === 'function') {
                        onAccept();
                    }

                    fetchPaged();
                } catch (err) {
                    errors.value = err.response.data.errors;
                }
            },
            reject: () => {
                if (typeof onReject === 'function') {
                    onReject();
                }
                return false;
            },
            onHide: () => {
                if (typeof onHide === 'function') {
                    onHide();
                }
                return false;
            },
        });
    };

    const contextLoading = (context, val) => {
        if (typeof val !== 'undefined') {
            ctxLoading.value[context] = val;
            return;
        }
        return ctxLoading[context];
    };
    watch(
        () => filters.value,
        () => {
            fetchPaged();
        },
        { deep: true, immediate: true }
    );
    return {
        serverPagination,
        filters,
        loading,
        enableLoader,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        progresses,
        statusOptions,
        selected,
        jsonToFormData,
        getAll,
        fetchDataById,
        onRequest,
        store,
        fetchPaged,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        clearFunction,
        toggleStatus,
        fetchDataBySlug,
        updateDropDown,
        notifySuccess,
        notifyError,
        changeStatusOtherColumn,
        ctxLoading,
        contextLoading,
        isFormValid,
        submitKendoForm,
    };
}
