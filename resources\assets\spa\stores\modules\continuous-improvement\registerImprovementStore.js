import useConfirm from '@spa/services/useConfirm';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useRegisterImprovementStore = defineStore('useRegisterImprovementStore', () => {
    const storeUrl = ref('v2/tenant/register-improvement');
    const confirm = useConfirm();

    const commonStoreProps = useCommonStore(storeUrl.value);
    const { selected, remove, fetchPaged, errors } = commonStoreProps;

    const userTypes = ref([]);
    const categories = ref([]);

    const getFormConstants = async () => {
        try {
            const response = await $http.get(`/api/${storeUrl.value}/form-constants`);
            console.log('defaults', response);
            userTypes.value = convertToTextValue(response.user_types);
            categories.value = convertToTextValue(response.catagory);
            return response;
        } catch (error) {
            console.error('Error fetching form constants:', error);
            throw error;
        }
    };

    const convertToTextValue = (obj) => {
        return Object.entries(obj).map(([key, value]) => ({
            text: value,
            value: key,
        }));
    };

    return {
        ...commonStoreProps,
        getFormConstants,
        userTypes,
        categories,
    };
});
