<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import DropdownMultiSelect from '@spa/components/Dropdown/DropdownMultiSelect.vue';

const props = defineProps({
    label: String,
    className: String,
    optionValue: String,
    optionLabel: String,
    disabled: Boolean,
    store: Object,
    modelValue: [String, Number, Array, Object],
    clearable: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: true,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: true,
    },
});

const emit = defineEmits(['update:modelValue']);

const allOptions = ref([]);
const loading = ref(false);
const searchValue = ref('');

const filteredOptions = computed(() => {
    if (!searchValue.value) return allOptions.value;
    const needle = searchValue.value.toLowerCase();
    return allOptions.value.filter((item) =>
        item[props.optionLabel]?.toLowerCase().includes(needle)
    );
});

onMounted(async () => {
    if (props.store && typeof props.store.fetchPaged === 'function') {
        loading.value = true;
        try {
            await props.store.fetchPaged();
            allOptions.value = props.store.all || [];
        } catch (error) {
            console.error('Error fetching options:', error);
        } finally {
            loading.value = false;
        }
    }
});

watch(
    () => props.store?.all,
    (newVal) => {
        if (newVal) {
            allOptions.value = newVal;
        }
    },
    { deep: true }
);

const computedValue = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

function onChange(event) {
    emit('update:modelValue', event.value);
}

function onFilterChange(event) {
    if (!event || !event.filter) return;

    const value = event.filter.value || '';
    searchValue.value = value;

    if (!props.store || !props.store.filters) return;

    props.store.filters.query = value;

    // For empty searches, refresh from server
    if (value === '') {
        loading.value = true;
        try {
            props.store.fetchPaged();
        } catch (e) {
            console.error('Error fetching options:', e);
        } finally {
            loading.value = false;
        }
    }
}
</script>

<template>
    <div :class="`async-select ${className}`">
        <DropdownMultiSelect
            v-if="multiple"
            :data-items="filteredOptions"
            :text-field="optionLabel"
            :value-field="optionValue"
            :data-item-key="optionValue"
            :filterable="true"
            :clear-button="clearable"
            :disabled="disabled"
            :readonly="readonly"
            :label="label"
            :loading="loading"
            :value-primitive="true"
            @filterchange="onFilterChange"
            v-model="computedValue"
            :style="{
                width: '100%',
                minWidth: '200px',
                maxWidth: '400px',
            }"
        >
        </DropdownMultiSelect>
        <DropDownList
            v-else
            :data-items="filteredOptions"
            :text-field="optionLabel"
            :value-field="optionValue"
            :data-item-key="optionValue"
            v-model="computedValue"
            :filterable="true"
            :clear-button="clearable"
            :disabled="disabled"
            :readonly="readonly"
            :label="label"
            :loading="loading"
            @filterchange="onFilterChange"
        />
    </div>
</template>
<style lang="scss">
.async-select {
    .k-multiselect {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}
</style>
