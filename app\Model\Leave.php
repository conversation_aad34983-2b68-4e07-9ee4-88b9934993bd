<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use DB;
use Auth;

class Leave extends Model {

    protected $table = 'rto_leave_info';

    public function saveLeaveInfo($collegeId, $request) {
        $userPosition = $request->input('userPosition');

        if ($userPosition == 'Staff') {
            $staffId = $request->input('userStaff');
        } else {
            $staffId = $request->input('userTeacher');
        }

        $userId = Auth::user()->id;
        $objLeave = new Leave();
        $objLeave->college_id = $collegeId;
        $objLeave->staff_id = $staffId;
        $objLeave->user_position = $userPosition;
        $objLeave->from_date = (date("Y-m-d", strtotime($request->input('from_date'))));
        $objLeave->to_date = (date("Y-m-d", strtotime($request->input('to_date'))));
        $objLeave->comment = $request->input('comment');
        $objLeave->created_by = $userId;
        $objLeave->updated_by = $userId;
        $objLeave->save();
    }

    public function editLeaveInfo($leaveId, $request) {

        $userId = Auth::user()->id;
        $objLeave = Leave::find($leaveId);
        $objLeave->from_date = (date("Y-m-d", strtotime($request->input('from_date'))));
        $objLeave->to_date = (date("Y-m-d", strtotime($request->input('to_date'))));
        $objLeave->comment = $request->input('comment');
        $objLeave->created_by = $userId;
        $objLeave->updated_by = $userId;

        $objLeave->save();
    }

    public function deleteLeaveInfo($leaveId) {
        return $objLeaveInfo = DB::table('rto_leave_info')->Where('id', $leaveId)->delete();
    }

    public function getLeavelist($collegeId, $perPage) {
        $data = Leave::where('rto_leave_info.college_id', $collegeId)
                ->join('rto_staff_and_teacher as usr', 'usr.id', '=', 'rto_leave_info.staff_id')
                ->select('rto_leave_info.*', DB::raw('concat(usr.name_title," ",usr.first_name," ",usr.last_name) as fullName'))
                ->paginate($perPage);
        
        return $data;
    }

    public function saveLeaveInfoByTeacher($staffId, $collegeId, $userId, $request) {
        
        $objLeave = new Leave();
        $objLeave->college_id = $collegeId;
        $objLeave->staff_id = $staffId;
        //$objLeave->user_position = $userPosition;
        $objLeave->from_date = (date("Y-m-d", strtotime($request->input('from_date'))));
        $objLeave->to_date = (date("Y-m-d", strtotime($request->input('to_date'))));
        $objLeave->comment = $request->input('comment');
        $objLeave->created_by = $userId;
        $objLeave->updated_by = $userId;
        $objLeave->save();
        
        return TRUE;
    }
    
    public function getLeaveListByTeacher($staffId, $collegeId, $perPage) {
        
        return Leave::from('rto_leave_info as rli')
                    ->where('rli.college_id', $collegeId)
                    ->where('rli.staff_id', $staffId)
                    ->leftjoin('rto_users', 'rto_users.id', '=', 'rli.created_by')
                    ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rli.staff_id')
                    ->select('rli.*',
                            'rto_users.username',
                            DB::raw('concat(rst.name_title," ",rst.first_name," ",rst.last_name) as staff'))
                    ->paginate($perPage);
    }
}
