<?php

namespace GalaxyAPI\Controllers;

use App\Model\PdfTemplate;
use Auth;
use GalaxyAPI\Requests\PdfTemplateRequest;
use GalaxyAPI\Resources\PdfTemplateResource;

class PdfTemplateController extends CrudBaseController
{
    public function __construct()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()->college_id,
        ];
        parent::__construct(
            model: PdfTemplate::class,
            storeRequest: PdfTemplateRequest::class,
            updateRequest: PdfTemplateRequest::class,
            resource: PdfTemplateResource::class,
        );
    }

    public function getPdfTemplateList($data)
    {
        $collegeId = Auth::user()->college_id;

        $pdfTemplates = PdfTemplate::where('college_id', $collegeId)->get();

        // Transform updated_at and type before passing to Resource
        foreach ($pdfTemplates as $template) {
            $template->updated_date = date('d-m-Y', strtotime($template->updated_at));
        }

        return PdfTemplateResource::collection($pdfTemplates);
    }

    public function updateDefaultTemplate(PdfTemplateRequest $request, $id)
    {
        $pdfTemplate = PdfTemplate::find($id);
        $pdfTemplate->default_template = $request->input('default_template');
        $pdfTemplate->save();

        return ajaxSuccess([], 'Default Template updated successfully.');
    }
}
