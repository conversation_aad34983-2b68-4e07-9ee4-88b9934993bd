<template>
    <async-grid-filters
        class="mb-4"
        :store="store"
        :has-search="hasSearch"
        :has-filters="hasFilters"
        :has-bulk-actions="hasBulkActions"
        :has-export="hasExport"
        :has-reset-filters="hasRestFilters"
        :has-create-action="hasCreateAction"
        @handel-reset="emit('handelReset')"
    >
        <template v-if="hasBulkActions" v-slot:bulk-actions>
            <slot name="bulk-actions" />
        </template>
        <template v-if="hasFilters" v-slot:filters>
            <slot name="filters" />
        </template>
    </async-grid-filters>
    <grid-wrapper
        :fullWidth="false"
        :rowHover="true"
        :rounded="true"
        class="flex-1 overflow-y-auto"
        :loading="store.loading"
    >
        <grid
            :style="gridStyle"
            :loading="store.loading"
            :loader="'loaderTemplate'"
            :data-items="store.all"
            :columns="gridColumns"
            :skip="(store.serverPagination.page - 1) * store.serverPagination.rowsPerPage"
            :take="store.serverPagination.rowsPerPage"
            :total="store.serverPagination.rowsNumber"
            :selected-field="enableSelection ? 'selected' : undefined"
            :selection-mode="enableSelection ? 'multiple' : undefined"
            @selectionchange="onSelectionChange"
            @headerselectionchange="onHeaderSelectionChange"
            @rowclick="onRowClick"
            @pagechange="onPageChange"
            :pageable="gridPageable"
            :scrollable="true"
            :resizable="true"
            :sortable="{ allowUnsort: false }"
            :sort="sort"
            @sortchange="onSortChange"
        >
            <template #loaderTemplate>
                <table-loader v-if="store.loading" />
            </template>
            <GridNoRecords>
                <empty-state />
            </GridNoRecords>
            <template #sortingHeaderCell="{ props }">
                <HeaderTemplate v-bind:props="props" :dir="getDirection(props.field)" />
            </template>
            <template #selectedTemplate="{ props }">
                <td>
                    <Checkbox
                        :checked="!!store.selected?.find((item) => item.id === props.dataItem.id)"
                        @update:checked="
                            (val) => {
                                if (val) {
                                    store.selected.push(props.dataItem);
                                } else {
                                    store.selected = store.selected.filter(
                                        (item) => item.id !== props.dataItem.id
                                    );
                                }
                            }
                        "
                    ></Checkbox>
                </td>
            </template>
            <template #selectedHeaderTemplate="{ props }">
                <Checkbox
                    :checked="store.selected?.length === store.all?.length"
                    @update:checked="
                        (val) => {
                            if (val) {
                                store.selected = [...store.all];
                            } else {
                                store.selected = [];
                            }
                        }
                    "
                ></Checkbox>
            </template>

            <template
                v-for="(col, i) in Array.isArray(columns) ? columns.filter((r) => r.replace) : []"
                :key="col?.name || i"
                v-slot:[`${col?.name}Template`]="{ props }"
            >
                <td>
                    <slot
                        :name="`body-cell-${col?.name}`"
                        :props="{
                            ...props,
                            rowForm: getRowForm(props.dataIndex),
                        }"
                    ></slot>
                </td>
            </template>
            <template
                v-for="(col, i) in Array.isArray(columns)
                    ? columns.filter((r) => r.columnFilter)
                    : []"
                :key="col.name || i"
                v-slot:[`${col?.name}ColumnMenu`]="{ props }"
            >
                <slot
                    :name="`column-menu-${col?.name}`"
                    :props="{
                        ...props,
                        rowForm: getRowForm(props.dataIndex),
                    }"
                ></slot>
            </template>
        </grid>
    </grid-wrapper>
</template>

<script setup>
import GridWrapper from '@spa/components/KendoGrid/GridWrapper';
import TableLoader from '@spa/components/KendoGrid/TableLoader';
import SortingIndicator from '@spa/components/KendoGrid/SortingIndicator';
import EmptyState from '@spa/components/KendoGrid/EmptyState';
import { ref, computed, watch } from 'vue';
import { Grid, GridNoRecords } from '@progress/kendo-vue-grid';
import AsyncGridFilters from '@spa/components/AsyncComponents/Grid/Partials/AsyncGridFilters.vue';
import ActionButtons from '@spa/components/KendoGrid/ActionButtons.vue';
import HeaderTemplate from '@spa/components/KendoGrid/templates/HeaderTemplate.vue';
import Checkbox from '@spa/components/Checkbox.vue';
const props = defineProps({
    store: { type: Object, required: true },
    columns: { type: Array, default: () => [] },
    enableSelection: {
        type: Boolean,
        default: false,
    },
    gridStyle: {
        default: { height: '70vh' },
    },
    selectedField: {
        type: String,
        default: 'selected',
    },
    hasSearch: { type: Boolean, default: true },
    hasFilters: { type: Boolean, default: true },
    hasBulkActions: { type: Boolean, default: true },
    hasExport: { type: Boolean, default: true },
    hasRestFilters: { type: Boolean, default: true },
    hasCreateAction: { type: Boolean, default: true },
});

const emit = defineEmits(['handelReset']);

const gridPageable = ref({
    buttonCount: 5,
    info: true,
    type: 'numeric',
    pageSizes: true,
    previousNext: true,
});

const sort = ref([]);

const getDirection = (field) => {
    const sortBy = props.store.serverPagination.sortBy;
    const sortField = props.columns.find((item) => item.field === field);
    if (!sortField) return null;
    const sortDirection = props.store.serverPagination.descending ? 'desc' : 'asc';
    if (sortBy === field) {
        return sortDirection;
    }
    return null;
};
const getRowForm = (dataIndex) => {
    const dataIndexStart =
        props.store.serverPagination.rowsPerPage * (props.store.serverPagination.page - 1);
    const currentIndex = dataIndex - dataIndexStart - 1;
    return props.store.form_rows[currentIndex] ?? {};
};

const gridColumns = computed(() => {
    const newColumns = [];
    if (props.enableSelection) {
        newColumns.push({
            field: 'selected',
            cell: 'selectedTemplate',
            headerCell: 'selectedHeaderTemplate',
            width: '50px',
            filterable: false,
            sortable: false,
            selectable: true,
        });
    }
    if (Array.isArray(props.columns)) {
        props.columns.forEach((column) => {
            const newColumn = { ...column };
            if (column.replace) {
                newColumn.cell = `${column.name}Template`;
            }
            if (column.columnFilter) {
                newColumn.columnMenu = `${column.name}ColumnMenu`;
            }
            if (column.sortable) {
                newColumn.headerCell = 'sortingHeaderCell';
            }
            newColumns.push(newColumn);
        });
    }
    return newColumns;
});

const onPageChange = (event) => {
    if (event.event.type === 'scroll') {
        return;
    }
    const page = Math.floor(event.page.skip / event.page.take) + 1;
    props.store.serverPagination.page = page;
    props.store.serverPagination.rowsPerPage = event.page.take;
    props.store.fetchPaged();
};

const onSelectionChange = (event) => {
    if (!props.store.selected) {
        props.store.selected = [];
    }
    if (event?.event?.target?.checked === true) {
        const exists = props.store.selected.find((item) => item?.id === event.dataItem?.id);
        if (exists) {
            return;
        }
        props.store.selected.push(event.dataItem);
    } else if (event?.event?.target?.checked === false) {
        props.store.selected = props.store.selected.filter(
            (item) => item?.id !== event.dataItem?.id
        );
    }
};

const onRowClick = (event) => {};

const onHeaderSelectionChange = (event) => {
    const checked = event.event.target.checked;
    props.store.all = props.store.all.map((item) => ({
        ...item,
        selected: checked,
    }));
    props.store.selected = checked ? props.store.all : [];
};

const onSortChange = (event) => {
    console.log('event', event);
    sort.value = event.sort;
    const field = event.sort[0].field;
    const isDescending = event.sort[0].dir === 'desc';
    props.store.serverPagination.sortBy = field;
    props.store.serverPagination.descending = isDescending;
    props.store.fetchPaged();
};
</script>
