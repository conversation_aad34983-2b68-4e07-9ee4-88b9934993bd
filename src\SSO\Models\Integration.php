<?php

namespace SSO\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use SSO\DTO\IntegrationConfig as DTOIntegrationConfig;
use SSO\DTO\Token;
use SSO\Facades\SSO;
use SSO\Repositories\Qtask;
use Support\Traits\CreaterUpdaterTrait;
use Support\Traits\HasJsonFields;

class Integration extends Model
{
    use CreaterUpdaterTrait;
    use HasJsonFields;
    use HasUlids;

    const STATUS_CANCELLED = 3;

    const STATUS_PAUSED = 2;

    const STATUS_ACTIVE = 1;

    const STATUS_INACTIVE = 0;

    protected $fillable = ['url', 'integration', 'data', 'token'];

    protected $casts = [
        'data' => 'json',
        'token' => 'json',
    ];

    public function scopeFindByKey($q, $key)
    {
        return $q->where('integration', $key)->first();
    }

    public function integrationUrl($register = false)
    {
        $this->updateJsonField([
            'integration_token' => Str::random(40),
            'integration_requested_at' => now(),
        ], 'data');

        // dd($this->url . '/' . ltrim(config(SSO::prefix() . '.config.integrations_path'), '/') . '?' . http_build_query([
        //     'redirect' => route('galaxy.integration.activate', [$this->id, $this->data['integration_token']]),
        //     'redirect_cancelled' => route('galaxy.integrations') . '?active=' . $this->key . '&error=',
        //     'request_from' => config('app.name'),
        //     'tenant' => tenant('id'),
        //     'register' => $register ? 1 : 0
        // ]));

        return $this->url.'/'.ltrim(config(SSO::prefix().'.config.integrations_path'), '/').'?'.http_build_query([
            'redirect' => route('galaxy.integration.activate', [$this->id, $this->data['integration_token']]),
            'redirect_cancelled' => route('galaxy.integrations').'?active='.$this->key.'&error=',
            'request_from' => config('app.name'),
            'tenant' => tenant('id'),
            'register' => $register ? 1 : 0,
        ]);
    }

    public function registrationUrl($url)
    {
        $this->updateJsonField([
            'integration_token' => Str::random(40),
            'integration_requested_at' => now(),
        ], 'data');

        return $url.'?'.http_build_query([
            'redirect' => route('galaxy.integration.activate', [$this->id, $this->data['integration_token']]),
            'redirect_cancelled' => route('galaxy.integrations').'?active='.$this->key.'&error=',
            'request_from' => config('app.name'),
            'tenant' => tenant('id'),

        ]);
    }

    public function getApiUrl()
    {
        switch ($this->integration) {
            default:
                return rtrim(config(SSO::prefix().'.integrations.'.$this->integration.'.home_url'), '/').'/api/v3';
        }
    }

    public function isActive()
    {
        return $this->status == self::STATUS_ACTIVE;
    }

    public function isPaused()
    {
        return $this->status == self::STATUS_PAUSED;
    }

    public function idProvisioned()
    {
        return $this->url != null;
    }

    public function activate($url = null, $tenantId = null)
    {
        $this->status = self::STATUS_ACTIVE;
        if ($url && validate_url($url)) {
            $this->url = $url;
        }
        $this->updateJsonField([
            'tenant_id' => $tenantId,
            'integration_token' => null,
            'integration_requested_at' => null,
            'activated_at' => now(),
        ]);

        return $this->save();
    }

    public function resume()
    {

        if (! $this->url || ! isset($this->data['tenant_id'])) {
            throw new \Exception('Tenant not provisioned');
        }

        $this->status = self::STATUS_ACTIVE;
        $this->updateJsonField([
            'resumed_at' => now(),
            'paused_at' => null,
        ]);
    }

    public function pause()
    {
        $this->status = self::STATUS_PAUSED;
        $this->updateJsonField([
            'paused_at' => now(),
        ]);
    }

    public function cancel()
    {
        $this->status = self::STATUS_CANCELLED;
        $this->token = null;
        $this->updateJsonField([
            'cancelled_at' => now(),
        ]);
    }

    public function getTable()
    {
        return config(SSO::prefix().'.config.integrations_table');
    }

    public function saveToken(Token $token)
    {

        $this->token = array_merge($token->toArray(), [
            'expires_in_datetime' => now()->addSeconds($token->expires_in ?? 60),
        ]);

        return $this->save();
    }

    public function getToken()
    {
        return Token::LazyFromArray($this->token);
    }

    public function ssoUrl()
    {
        return $this->url.'/galaxy/authorize';
    }

    public function disconnect()
    {
        return $this->cancel();
    }

    public function getConfig()
    {
        // dd($this, $this->key, config(SSO::prefix() . '.integrations'));
        return DTOIntegrationConfig::LazyFromArray(config(SSO::prefix().'.integrations.'.$this->integration));
    }

    public function repo()
    {
        // dd($this->data);
        switch ($this->integration) {

            case 'qtask':
                return new Qtask($this, $this->data['tenant_id']);
        }
    }

    public static function Repository($key)
    {
        return Integration::findByKey($key)->repo();
    }
}
